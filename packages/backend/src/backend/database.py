from collections.abc import Generator
from contextlib import contextmanager

from sqlalchemy import Connection, Engine, create_engine, text

from .config import settings

connect_args = {"check_same_thread": False}
engine: Engine = create_engine(
    settings.DATABASE_URI, echo=True, connect_args=connect_args
)


def init_db():
    """Initialize the database.

    Creates all tables using raw SQL DDL statements.
    """
    with engine.connect() as conn:
        # Create users table
        conn.execute(
            text("""
            CREATE TABLE IF NOT EXISTS "user" (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                steam_id TEXT UNIQUE NOT NULL,
                username TEXT,
                profile_picture_url TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
        """)
        )

        # Create tracking_details table
        conn.execute(
            text("""
            CREATE TABLE IF NOT EXISTS tracking_details (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                authentication_code TEXT NOT NULL,
                initial_match_token TEXT NOT NULL,
                user_id INTEGER UNIQUE,
                <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (user_id) REFERENCES "user" (id)
            )
        """)
        )

        # Create demo_file table
        conn.execute(
            text("""
            CREATE TABLE IF NOT EXISTS demo_file (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT NOT NULL,
                file_path TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'PENDING_DOWNLOAD',
                error_message TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
        """)
        )

        # Create processing_job table
        conn.execute(
            text("""
            CREATE TABLE IF NOT EXISTS processing_job (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                demo_file_id INTEGER NOT NULL,
                priority INTEGER NOT NULL DEFAULT 0,
                status TEXT NOT NULL DEFAULT 'QUEUED',
                attempts INTEGER NOT NULL DEFAULT 0,
                last_error TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (demo_file_id) REFERENCES demo_file (id)
            )
        """)
        )

        # Create match table
        conn.execute(
            text("""
            CREATE TABLE IF NOT EXISTS "match" (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                map_name TEXT NOT NULL,
                demo_file_id INTEGER,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (demo_file_id) REFERENCES demo_file (id)
            )
        """)
        )

        # Create match_player table
        conn.execute(
            text("""
            CREATE TABLE IF NOT EXISTS match_player (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                steam_id TEXT NOT NULL,
                name TEXT NOT NULL,
                team TEXT NOT NULL DEFAULT 'UNKNOWN',
                match_id INTEGER,
                FOREIGN KEY (match_id) REFERENCES "match" (id)
            )
        """)
        )

        # Create match_round table
        conn.execute(
            text("""
            CREATE TABLE IF NOT EXISTS match_round (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                round_number INTEGER NOT NULL,
                winner TEXT NOT NULL DEFAULT 'UNKNOWN',
                win_reason TEXT NOT NULL DEFAULT 'UNKNOWN',
                ct_score INTEGER NOT NULL DEFAULT 0,
                t_score INTEGER NOT NULL DEFAULT 0,
                match_id INTEGER,
                FOREIGN KEY (match_id) REFERENCES "match" (id)
            )
        """)
        )

        # Create indexes for frequently queried columns
        
        # Index on user.steam_id for fast user lookup
        conn.execute(
            text("""
            CREATE INDEX IF NOT EXISTS idx_user_steam_id ON "user" (steam_id)
            """)
        )
        
        # Index on demo_file.match_id for demo lookup by match
        conn.execute(
            text("""
            CREATE INDEX IF NOT EXISTS idx_demo_file_match_id ON demo_file (match_id)
            """)
        )
        
        # Index on demo_file.status for filtering by status
        conn.execute(
            text("""
            CREATE INDEX IF NOT EXISTS idx_demo_file_status ON demo_file (status)
            """)
        )
        
        # Index on processing_job.demo_file_id for job lookup
        conn.execute(
            text("""
            CREATE INDEX IF NOT EXISTS idx_processing_job_demo_file_id ON processing_job (demo_file_id)
            """)
        )
        
        # Index on processing_job.status for filtering by status
        conn.execute(
            text("""
            CREATE INDEX IF NOT EXISTS idx_processing_job_status ON processing_job (status)
            """)
        )
        
        # Index on match_player.match_id for player lookup by match
        conn.execute(
            text("""
            CREATE INDEX IF NOT EXISTS idx_match_player_match_id ON match_player (match_id)
            """)
        )
        
        # Index on match_player.steam_id for player lookup by steam_id
        conn.execute(
            text("""
            CREATE INDEX IF NOT EXISTS idx_match_player_steam_id ON match_player (steam_id)
            """)
        )
        
        # Index on match_round.match_id for round lookup by match
        conn.execute(
            text("""
            CREATE INDEX IF NOT EXISTS idx_match_round_match_id ON match_round (match_id)
            """)
        )
        
        conn.commit()


@contextmanager
def get_connection() -> Generator[Connection]:
    """Get a database connection.

    Yields:
        Connection: A SQLAlchemy connection for database operations
    """
    with engine.connect() as connection:
        yield connection


def get_connection_dependency():
    """FastAPI dependency for getting a database connection.

    Yields:
        Connection: A SQLAlchemy connection for database operations
    """
    with get_connection() as connection:
        yield connection
