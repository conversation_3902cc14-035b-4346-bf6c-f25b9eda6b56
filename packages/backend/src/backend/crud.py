from datetime import UTC, datetime
from typing import Annotated

from fastapi import Depends, HTTPException, status
from sqlalchemy import Connection, text

from backend.clients.steam_api import SteamAPI, get_steam_api
from backend.database import get_connection_dependency
from backend.schemas import (
    DemoFileCreate,
    DemoFileRead,
    MatchPlayerRead,
    MatchRead,
    MatchRoundRead,
    TrackingDetailsCreate,
    TrackingDetailsRead,
    UserCreate,
    UserRead,
    UserReadWithTracking,
)

# Note: SQLAlchemy Row attributes are typed as Any, but we use Pydantic schemas
# for runtime validation and proper typing of return values. This provides
# type safety while working with raw SQL queries.


async def create_user(
    user: UserCreate,
    conn: Annotated[Connection, Depends(get_connection_dependency)],
    steam_api: Annotated[SteamAPI, Depends(get_steam_api)],
) -> UserRead | None:
    """Create a new user.

    Args:
        user: User data to create
        conn: Database connection
        steam_api: Steam API client

    Returns:
        Created user record
    """
    existing_user = read_user_by_steam_id(user.steam_id, conn, raise_if_not_found=False)
    if existing_user:
        return None

    steam_profile = await steam_api.get_player_summary(user.steam_id)

    # Insert new user
    result = conn.execute(
        text("""
        INSERT INTO "user" (steam_id, username, profile_picture_url, created_at)
        VALUES (:steam_id, :username, :profile_picture_url, :created_at)
        RETURNING id, steam_id, username, profile_picture_url, created_at
    """),
        {
            "steam_id": user.steam_id,
            "username": str(steam_profile.personaname),
            "profile_picture_url": steam_profile.avatarfull,
            "created_at": datetime.now(UTC),
        },
    )

    user_row = result.fetchone()
    if user_row:
        conn.commit()
        # Use Pydantic to validate and type the database result
        return UserRead(
            id=user_row.id,
            steam_id=user_row.steam_id,
            username=user_row.username,
            profile_picture_url=user_row.profile_picture_url,
            created_at=user_row.created_at,
        )
    return None


def read_user_by_steam_id(
    steam_id: str,
    conn: Annotated[Connection, Depends(get_connection_dependency)],
    raise_if_not_found: bool = True,
) -> UserRead | None:
    """Read a user by their Steam ID.

    Args:
        steam_id: Steam ID of the user
        conn: Database connection
        raise_if_not_found: Whether to raise an HTTPException if the user is not found

    Returns:
        User record

    Raises:
        HTTPException: If user is not found and raise_if_not_found is True
    """
    result = conn.execute(
        text("""
        SELECT id, steam_id, username, profile_picture_url, created_at
        FROM "user"
        WHERE steam_id = :steam_id
    """),
        {"steam_id": steam_id},
    )

    user_row = result.fetchone()
    if user_row is None and raise_if_not_found:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    if user_row:
        # Use Pydantic to validate and type the database result
        return UserRead(
            id=user_row.id,
            steam_id=user_row.steam_id,
            username=user_row.username,
            profile_picture_url=user_row.profile_picture_url,
            created_at=user_row.created_at,
        )
    return None


def read_user(
    user_id: int, conn: Annotated[Connection, Depends(get_connection_dependency)]
) -> UserReadWithTracking:
    """Read a user by their ID.

    Args:
        user_id: ID of the user
        conn: Database connection

    Returns:
        User record with tracking information

    Raises:
        HTTPException: If user is not found
    """
    result = conn.execute(
        text("""
        SELECT u.id, u.steam_id, u.username, u.profile_picture_url, u.created_at,
               CASE WHEN t.id IS NOT NULL THEN 1 ELSE 0 END as has_tracking
        FROM "user" u
        LEFT JOIN tracking_details t ON u.id = t.user_id
        WHERE u.id = :user_id
    """),
        {"user_id": user_id},
    )

    user_row = result.fetchone()
    if user_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Use Pydantic to validate and type the database result
    return UserReadWithTracking(
        id=user_row.id,
        steam_id=user_row.steam_id,
        username=user_row.username,
        profile_picture_url=user_row.profile_picture_url,
        created_at=user_row.created_at,
        has_tracking=bool(user_row.has_tracking),
    )


def is_user_registered(
    steam_id: str, conn: Annotated[Connection, Depends(get_connection_dependency)]
) -> bool:
    """Check if a user is registered.

    Args:
        steam_id: Steam ID of the user
        conn: Database connection

    Returns:
        True if user is registered, False otherwise
    """
    user = read_user_by_steam_id(steam_id, conn, raise_if_not_found=False)
    return user is not None


def create_tracking_details(
    user_id: int,
    tracking: TrackingDetailsCreate,
    conn: Connection,
) -> TrackingDetailsRead:
    """Create tracking details for a user.

    Args:
        user_id: ID of the user
        tracking: Tracking details to create
        conn: Database connection

    Returns:
        Created tracking details record

    Raises:
        HTTPException: If user is not found
    """
    # Check if user exists
    user_result = conn.execute(
        text("""
        SELECT id FROM "user" WHERE id = :user_id
    """),
        {"user_id": user_id},
    )

    if user_result.fetchone() is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Check if user already has tracking
    tracking_result = conn.execute(
        text("""
        SELECT id FROM tracking_details WHERE user_id = :user_id
    """),
        {"user_id": user_id},
    )

    if tracking_result.fetchone() is not None:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT, detail="User already has tracking"
        )

    # Insert tracking details
    result = conn.execute(
        text("""
        INSERT INTO tracking_details (authentication_code, initial_match_token, user_id)
        VALUES (:authentication_code, :initial_match_token, :user_id)
        RETURNING id, authentication_code, initial_match_token, user_id
    """),
        {
            "authentication_code": tracking.authentication_code,
            "initial_match_token": tracking.initial_match_token,
            "user_id": user_id,
        },
    )

    tracking_row = result.fetchone()
    if tracking_row is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create tracking details",
        )

    conn.commit()

    # Use Pydantic to validate and type the database result
    return TrackingDetailsRead(
        id=tracking_row.id,
        authentication_code=tracking_row.authentication_code,
        initial_match_token=tracking_row.initial_match_token,
        user_id=tracking_row.user_id,
    )


def read_tracking_details(
    user_id: int, conn: Annotated[Connection, Depends(get_connection_dependency)]
) -> TrackingDetailsRead:
    """Read tracking details for a user.

    Args:
        user_id: ID of the user
        conn: Database connection

    Returns:
        Tracking details record

    Raises:
        HTTPException: If user is not found or has no tracking
    """
    # Check if user exists and get tracking details
    result = conn.execute(
        text("""
        SELECT u.id as user_id, t.id, t.authentication_code, t.initial_match_token
        FROM "user" u
        LEFT JOIN tracking_details t ON u.id = t.user_id
        WHERE u.id = :user_id
    """),
        {"user_id": user_id},
    )

    row = result.fetchone()
    if row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    if row.id is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User has no tracking details"
        )

    # Use Pydantic to validate and type the database result
    return TrackingDetailsRead(
        id=row.id,
        authentication_code=row.authentication_code,
        initial_match_token=row.initial_match_token,
        user_id=row.user_id,
    )


def create_demo_file(demo: DemoFileCreate, conn: Connection) -> DemoFileRead:
    """Create a new demo file record.

    Args:
        demo: The demo file data to create
        conn: Database connection

    Returns:
        Created demo file record

    Raises:
        HTTPException: If demo file creation fails
    """
    result = conn.execute(
        text("""
        INSERT INTO demo_file (match_id, file_path, status, created_at, updated_at)
        VALUES (:match_id, :file_path, :status, :created_at, :updated_at)
        RETURNING id, match_id, file_path, status, error_message, created_at, updated_at
    """),
        {
            "match_id": demo.match_id,
            "file_path": demo.file_path,
            "status": demo.status.value,
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC),
        },
    )

    demo_row = result.fetchone()
    if demo_row is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create demo file",
        )

    conn.commit()

    # Use Pydantic to validate and type the database result
    return DemoFileRead(
        id=demo_row.id,
        match_id=demo_row.match_id,
        file_path=demo_row.file_path,
        status=demo_row.status,
        error_message=demo_row.error_message,
        created_at=demo_row.created_at,
        updated_at=demo_row.updated_at,
        processing_jobs=[],
        match=None,
    )


def read_demo_file(demo_id: int, conn: Connection) -> DemoFileRead:
    """Read a demo file by ID.

    Args:
        demo_id: ID of the demo file
        conn: Database connection

    Returns:
        Demo file record

    Raises:
        HTTPException: If demo file is not found
    """
    result = conn.execute(
        text("""
        SELECT id, match_id, file_path, status, error_message, created_at, updated_at
        FROM demo_file
        WHERE id = :demo_id
    """),
        {"demo_id": demo_id},
    )

    demo_row = result.fetchone()
    if demo_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Demo file not found"
        )

    return DemoFileRead(
        id=demo_row.id,
        match_id=demo_row.match_id,
        file_path=demo_row.file_path,
        status=demo_row.status,
        error_message=demo_row.error_message,
        created_at=demo_row.created_at,
        updated_at=demo_row.updated_at,
        processing_jobs=[],
        match=None,
    )


def read_demo_file_by_match(match_id: str, conn: Connection) -> DemoFileRead:
    """Read a demo file by match ID.

    Args:
        match_id: Match ID of the demo file
        conn: Database connection

    Returns:
        Demo file record

    Raises:
        HTTPException: If demo file is not found
    """
    result = conn.execute(
        text("""
        SELECT id, match_id, file_path, status, error_message, created_at, updated_at
        FROM demo_file
        WHERE match_id = :match_id
    """),
        {"match_id": match_id},
    )

    demo_row = result.fetchone()
    if demo_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Demo file not found"
        )

    return DemoFileRead(
        id=demo_row.id,
        match_id=demo_row.match_id,
        file_path=demo_row.file_path,
        status=demo_row.status,
        error_message=demo_row.error_message,
        created_at=demo_row.created_at,
        updated_at=demo_row.updated_at,
        processing_jobs=[],
        match=None,
    )


def list_demo_files(conn: Connection) -> list[DemoFileRead]:
    """List all demo files.

    Args:
        conn: Database connection

    Returns:
        List of demo file records
    """
    result = conn.execute(
        text("""
        SELECT id, match_id, file_path, status, error_message, created_at, updated_at
        FROM demo_file
        ORDER BY created_at DESC
    """)
    )

    demo_files = []
    for row in result:
        demo_files.append(
            DemoFileRead(
                id=row.id,
                match_id=row.match_id,
                file_path=row.file_path,
                status=row.status,
                error_message=row.error_message,
                created_at=row.created_at,
                updated_at=row.updated_at,
                processing_jobs=[],
                match=None,
            )
        )

    return demo_files


def create_match(match_data: dict, demo_file_id: int, conn: Connection) -> None:
    """Create a match record with players and rounds.

    Args:
        match_data: Dictionary containing match information
        demo_file_id: ID of the demo file this match was extracted from
        conn: Database connection

    Returns:
        None
    """
    # Insert match record
    map_name = match_data.get("map", {}).get("name", "unknown")

    match_result = conn.execute(
        text("""
        INSERT INTO "match" (map_name, demo_file_id, created_at)
        VALUES (:map_name, :demo_file_id, :created_at)
        RETURNING id
    """),
        {
            "map_name": map_name,
            "demo_file_id": demo_file_id,
            "created_at": datetime.now(UTC),
        },
    )

    match_row = match_result.fetchone()
    if match_row is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create match",
        )

    match_id = match_row.id

    # Insert players
    players_data = match_data.get("players", [])
    for player in players_data:
        conn.execute(
            text("""
            INSERT INTO match_player (steam_id, name, team, match_id)
            VALUES (:steam_id, :name, :team, :match_id)
        """),
            {
                "steam_id": player.get("steam_id", ""),
                "name": player.get("name", ""),
                "team": player.get("team", "UNKNOWN"),
                "match_id": match_id,
            },
        )

    # Insert rounds
    rounds_data = match_data.get("rounds", [])
    for round_data in rounds_data:
        conn.execute(
            text("""
            INSERT INTO match_round (round_number, winner, win_reason, ct_score, t_score, match_id)
            VALUES (:round_number, :winner, :win_reason, :ct_score, :t_score, :match_id)
        """),
            {
                "round_number": round_data.get("round_number", 0),
                "winner": round_data.get("winner", "UNKNOWN"),
                "win_reason": round_data.get("win_reason", "UNKNOWN"),
                "ct_score": round_data.get("ct_score", 0),
                "t_score": round_data.get("t_score", 0),
                "match_id": match_id,
            },
        )

    conn.commit()


def read_match(match_id: int, conn: Connection) -> MatchRead:
    """Read a match by ID.

    Args:
        match_id: ID of the match
        conn: Database connection

    Returns:
        Match record with players and rounds

    Raises:
        HTTPException: If match is not found
    """
    # Get match data
    match_result = conn.execute(
        text("""
        SELECT id, map_name, demo_file_id, created_at
        FROM "match"
        WHERE id = :match_id
    """),
        {"match_id": match_id},
    )

    match_row = match_result.fetchone()
    if match_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Match not found"
        )

    # Get players
    players_result = conn.execute(
        text("""
        SELECT id, steam_id, name, team, match_id
        FROM match_player
        WHERE match_id = :match_id
    """),
        {"match_id": match_id},
    )

    players = []
    for player_row in players_result:
        players.append(
            MatchPlayerRead(
                id=player_row.id,
                steam_id=player_row.steam_id,
                name=player_row.name,
                team=player_row.team,
                match_id=player_row.match_id,
            )
        )

    # Get rounds
    rounds_result = conn.execute(
        text("""
        SELECT id, round_number, winner, win_reason, ct_score, t_score, match_id
        FROM match_round
        WHERE match_id = :match_id
        ORDER BY round_number
    """),
        {"match_id": match_id},
    )

    rounds = []
    for round_row in rounds_result:
        rounds.append(
            MatchRoundRead(
                id=round_row.id,
                round_number=round_row.round_number,
                winner=round_row.winner,
                win_reason=round_row.win_reason,
                ct_score=round_row.ct_score,
                t_score=round_row.t_score,
                match_id=round_row.match_id,
            )
        )

    return MatchRead(
        id=match_row.id,
        map_name=match_row.map_name,
        demo_file_id=match_row.demo_file_id,
        created_at=match_row.created_at,
        players=players,
        rounds=rounds,
    )


def read_match_by_demo_file(demo_file_id: int, conn: Connection) -> MatchRead:
    """Read a match by demo file ID.

    Args:
        demo_file_id: ID of the demo file
        conn: Database connection

    Returns:
        Match record with players and rounds

    Raises:
        HTTPException: If match is not found
    """
    # Get match data
    match_result = conn.execute(
        text("""
        SELECT id, map_name, demo_file_id, created_at
        FROM "match"
        WHERE demo_file_id = :demo_file_id
    """),
        {"demo_file_id": demo_file_id},
    )

    match_row = match_result.fetchone()
    if match_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Match not found for demo file",
        )

    # Use the existing read_match function to get full match data
    return read_match(match_row.id, conn)


def list_matches(conn: Connection) -> list[MatchRead]:
    """List all matches.

    Args:
        conn: Database connection

    Returns:
        List of match records with players and rounds
    """
    # Optimized query to get all matches with their data in one go
    matches_result = conn.execute(
        text("""
        SELECT id, map_name, demo_file_id, created_at
        FROM "match"
        ORDER BY created_at DESC
    """)
    )

    # Get all match IDs for batch loading
    match_rows = matches_result.fetchall()
    if not match_rows:
        return []

    match_ids = [row.id for row in match_rows]
    
    # Batch load all players for all matches
    players_result = conn.execute(
        text("""
        SELECT match_id, steam_id, name, team
        FROM match_player
        WHERE match_id IN :match_ids
        ORDER BY match_id, id
        """),
        {"match_ids": tuple(match_ids)}
    )
    
    # Batch load all rounds for all matches
    rounds_result = conn.execute(
        text("""
        SELECT match_id, round_number, winner, win_reason, ct_score, t_score
        FROM match_round
        WHERE match_id IN :match_ids
        ORDER BY match_id, round_number
        """),
        {"match_ids": tuple(match_ids)}
    )
    
    # Group players and rounds by match_id
    players_by_match = {}
    for player_row in players_result:
        match_id = player_row.match_id
        if match_id not in players_by_match:
            players_by_match[match_id] = []
        players_by_match[match_id].append(
            MatchPlayerRead(
                steam_id=player_row.steam_id,
                name=player_row.name,
                team=player_row.team,
            )
        )
    
    rounds_by_match = {}
    for round_row in rounds_result:
        match_id = round_row.match_id
        if match_id not in rounds_by_match:
            rounds_by_match[match_id] = []
        rounds_by_match[match_id].append(
            MatchRoundRead(
                round_number=round_row.round_number,
                winner=round_row.winner,
                win_reason=round_row.win_reason,
                ct_score=round_row.ct_score,
                t_score=round_row.t_score,
            )
        )
    
    # Build match objects
    matches = []
    for match_row in match_rows:
        match_id = match_row.id
        matches.append(
            MatchRead(
                id=match_id,
                map_name=match_row.map_name,
                demo_file_id=match_row.demo_file_id,
                created_at=match_row.created_at,
                players=players_by_match.get(match_id, []),
                rounds=rounds_by_match.get(match_id, []),
            )
        )

    return matches


def update_demo_file(
    demo_id: int, update_data: dict[str, str], conn: Connection
) -> DemoFileRead:
    """Update a demo file record.

    Args:
        demo_id: ID of the demo file to update
        update_data: Dictionary of fields to update
        conn: Database connection

    Returns:
        Updated demo file record

    Raises:
        HTTPException: If demo file is not found
    """
    # Allowlist of valid fields to prevent SQL injection
    ALLOWED_FIELDS = {"status", "error_message"}
    
    # Build safe update query using allowlist
    set_clauses = []
    params = {"demo_id": demo_id, "updated_at": datetime.now(UTC)}

    for key, value in update_data.items():
        if key in ALLOWED_FIELDS:
            # Safe to use field name since it's from allowlist
            set_clauses.append(f"{key} = :{key}")
            params[key] = value

    if not set_clauses:
        # No valid fields to update, just return current record
        return read_demo_file(demo_id, conn)

    set_clauses.append("updated_at = :updated_at")

    # Build query with allowlisted field names
    query = f"""
        UPDATE demo_file
        SET {", ".join(set_clauses)}
        WHERE id = :demo_id
        RETURNING id, match_id, file_path, status, error_message, created_at, updated_at
    """

    result = conn.execute(text(query), params)
    demo_row = result.fetchone()

    if demo_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Demo file not found"
        )

    conn.commit()

    return DemoFileRead(
        id=demo_row.id,
        match_id=demo_row.match_id,
        file_path=demo_row.file_path,
        status=demo_row.status,
        error_message=demo_row.error_message,
        created_at=demo_row.created_at,
        updated_at=demo_row.updated_at,
        processing_jobs=[],
        match=None,
    )
